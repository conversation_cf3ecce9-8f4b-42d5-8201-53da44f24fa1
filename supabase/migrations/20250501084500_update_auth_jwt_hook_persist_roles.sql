-- Migration: Persist roles array into auth.users.raw_user_meta_data during JWT hook
-- Generated on 2025-05-01

-- Replace the auth.jwt(event jsonb) function so it also writes the roles array
-- into auth.users.raw_user_meta_data.  This lets supabase.auth.getUser() expose
-- user_metadata.roles without any client-side JWT parsing.

CREATE OR REPLACE FUNCTION auth.jwt(event jsonb)
RETURNS jsonb
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public, extensions, tenants, auth
AS $$
DECLARE
  user_id      uuid;
  tenant_id    uuid;
  primary_role tenants.users.role%TYPE;
  firm_role    text;
  is_super_admin boolean;
  app_metadata jsonb;
  roles_arr    jsonb;
  claims       jsonb;
BEGIN
  -- Extract uid from incoming claims
  user_id := (event->>'sub')::uuid;

  -- Pull tenant + roles + super admin status from tenants.users
  SELECT tu.tenant_id, tu.role, tu.firm_role, COALESCE(tu.is_super_admin, false)
  INTO   tenant_id, primary_role, firm_role, is_super_admin
  FROM   tenants.users tu
  WHERE  tu.id = user_id; -- "id" in tenants.users matches auth uid

  IF tenant_id IS NULL OR primary_role IS NULL THEN
    RETURN event;
  END IF;

  ---------------------------------------------------------------------------
  -- Build roles array and app_metadata
  ---------------------------------------------------------------------------
  roles_arr := jsonb_build_array(primary_role, firm_role);

  app_metadata := jsonb_build_object(
    'tenant_id', tenant_id,
    'roles',     roles_arr
  );

  claims := event;
  claims := jsonb_set(claims, '{role}', to_jsonb(primary_role), true);
  claims := jsonb_set(claims, '{is_super_admin}', to_jsonb(is_super_admin), true);
  claims := jsonb_set(claims, '{app_metadata}', app_metadata, true);

  ---------------------------------------------------------------------------
  -- Persist roles array AND tenant_id into auth.users.raw_user_meta_data
  ---------------------------------------------------------------------------
  UPDATE auth.users
  SET    raw_user_meta_data = coalesce(raw_user_meta_data, '{}'::jsonb)
           || jsonb_build_object('roles', roles_arr, 'tenant_id', tenant_id)
  WHERE  id = user_id;

  RETURN claims;
END;
$$;

GRANT EXECUTE ON FUNCTION auth.jwt(jsonb) TO authenticated, service_role;
