-- Migration to add is_super_admin column to tenants.users table
-- This enables JWT-based super admin access control

-- Add is_super_admin column to tenants.users table
ALTER TABLE tenants.users 
ADD COLUMN is_super_admin BOOLEAN DEFAULT FALSE;

-- Set is_super_admin = true for the designated super admin user
-- Using the email from the user's memory: j<PERSON><PERSON><PERSON><PERSON>@gmail.com
UPDATE tenants.users 
SET is_super_admin = TRUE 
WHERE email = 'jka<PERSON><PERSON><PERSON>@gmail.com';

-- Add comment for documentation
COMMENT ON COLUMN tenants.users.is_super_admin IS 'Indicates if user has super admin privileges for platform operations';

-- Create index for performance (optional but recommended)
CREATE INDEX idx_users_is_super_admin ON tenants.users(is_super_admin) WHERE is_super_admin = TRUE;
