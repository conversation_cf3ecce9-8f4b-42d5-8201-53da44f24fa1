# Role Matrix - PI Lawyer AI

**Date**: 2025-01-04  
**Purpose**: Define role-based access control (RBAC) for the PI Lawyer AI platform  
**Updated**: Added Super Admin role for platform operations

## Role Hierarchy

### Platform Level

| Role | Description | JWT Claim | Access Level |
|------|-------------|-----------|--------------|
| **Super Admin** | Platform operations and system administration | `is_super_admin: true` | Full platform access |

### Tenant Level

| Role | Description | JWT Claim | Access Level |
|------|-------------|-----------|--------------|
| **Partner** | Law firm owner/partner | `role: "partner"` | Full tenant admin access |
| **Attorney** | Licensed attorney | `role: "attorney"` | Case management, client interactions |
| **Paralegal** | Legal assistant | `role: "paralegal"` | Support tasks, document management |
| **Staff** | Administrative staff | `role: "staff"` | Limited administrative access |
| **Client** | Law firm client | `role: "client"` | Personal case portal access |

## Access Control Matrix

### Route Access

| Route Pattern | Super Admin | Partner | Attorney | Paralegal | Staff | Client |
|---------------|-------------|---------|----------|-----------|-------|--------|
| `/superadmin/*` | ✅ | ❌ | ❌ | ❌ | ❌ | ❌ |
| `/admin/*` | ✅ | ✅ | ❌ | ❌ | ❌ | ❌ |
| `/dashboard` | ✅ | ✅ | ✅ | ✅ | ✅ | ❌ |
| `/client-portal` | ✅ | ✅ | ✅ | ✅ | ✅ | ✅ |
| `/cases` | ✅ | ✅ | ✅ | ✅ | ✅ | ❌ |
| `/documents` | ✅ | ✅ | ✅ | ✅ | ✅ | ✅ |
| `/calendar` | ✅ | ✅ | ✅ | ✅ | ✅ | ❌ |
| `/tasks` | ✅ | ✅ | ✅ | ✅ | ✅ | ❌ |
| `/clients` | ✅ | ✅ | ✅ | ✅ | ✅ | ❌ |
| `/users` | ✅ | ✅ | ❌ | ❌ | ❌ | ❌ |
| `/settings` | ✅ | ✅ | ❌ | ❌ | ❌ | ❌ |
| `/billing` | ✅ | ✅ | ❌ | ❌ | ❌ | ❌ |

### Feature Access

| Feature | Super Admin | Partner | Attorney | Paralegal | Staff | Client |
|---------|-------------|---------|----------|-----------|-------|--------|
| **Platform Management** | ✅ | ❌ | ❌ | ❌ | ❌ | ❌ |
| Model Configuration | ✅ | ❌ | ❌ | ❌ | ❌ | ❌ |
| Prompt Management | ✅ | ❌ | ❌ | ❌ | ❌ | ❌ |
| Tenant Quotas | ✅ | ❌ | ❌ | ❌ | ❌ | ❌ |
| Security Overview | ✅ | ❌ | ❌ | ❌ | ❌ | ❌ |
| **Tenant Management** | ✅ | ✅ | ❌ | ❌ | ❌ | ❌ |
| User Management | ✅ | ✅ | ❌ | ❌ | ❌ | ❌ |
| Billing & Subscriptions | ✅ | ✅ | ❌ | ❌ | ❌ | ❌ |
| Tenant Settings | ✅ | ✅ | ❌ | ❌ | ❌ | ❌ |
| **Case Management** | ✅ | ✅ | ✅ | ✅ | ✅ | ❌ |
| Create Cases | ✅ | ✅ | ✅ | ❌ | ❌ | ❌ |
| View Cases | ✅ | ✅ | ✅ | ✅ | ✅ | Own Only |
| Edit Cases | ✅ | ✅ | ✅ | Assigned Only | ❌ | ❌ |
| **Document Management** | ✅ | ✅ | ✅ | ✅ | ✅ | Own Only |
| **Templates** | ✅ | ✅ | ✅ | ❌ | ✅ | ❌ |

## Implementation Details

### JWT Claims Structure

```typescript
interface JwtPayload {
  sub: string;                    // User ID
  exp: number;                    // Expiration timestamp
  iat?: number;                   // Issued at timestamp
  role?: string;                  // Tenant-level role
  tenant_id?: string;             // Tenant ID
  email?: string;                 // User email
  is_super_admin?: boolean;       // Platform super admin flag
}
```

### RBAC Hook Usage

```typescript
import { useRbac } from '@/hooks/useRbac';

function MyComponent() {
  const rbac = useRbac();
  
  // Check specific roles
  if (rbac.isSuperAdmin()) {
    // Super admin only content
  }
  
  if (rbac.isAdmin()) {
    // Partner only content
  }
  
  if (rbac.hasAccess('cases')) {
    // Case management content
  }
}
```

### Route Guards

#### Super Admin Guard
```typescript
import SuperAdminGuard from '@/components/auth/SuperAdminGuard';

<SuperAdminGuard fallbackPath="/dashboard">
  <SuperAdminContent />
</SuperAdminGuard>
```

#### Role-Based Component
```typescript
import { RoleBasedComponent } from '@/components/auth/RoleBasedComponent';

<RoleBasedComponent allowedRoles={['partner', 'attorney']}>
  <AdminContent />
</RoleBasedComponent>
```

### Middleware Protection

The middleware automatically protects routes based on:

1. **Super Admin Routes** (`/superadmin/*`): JWT `is_super_admin` claim
2. **Tenant Routes** (`/admin/*`, `/dashboard`, etc.): JWT `role` claim
3. **Public Routes**: No authentication required

### Database Schema

```sql
-- tenants.users table
ALTER TABLE tenants.users 
ADD COLUMN is_super_admin BOOLEAN DEFAULT FALSE;

-- Set super admin for designated user
UPDATE tenants.users 
SET is_super_admin = TRUE 
WHERE email = '<EMAIL>';
```

### Security Considerations

1. **Super Admin Access**: Only one designated user should have `is_super_admin = true`
2. **JWT Claims**: Claims are generated server-side and cannot be tampered with
3. **Route Protection**: Both middleware and client-side guards provide defense in depth
4. **Audit Logging**: All super admin actions should be logged for security auditing

## Testing

### Unit Tests
- `frontend/src/hooks/__tests__/useRbac.test.ts`
- `frontend/src/components/auth/__tests__/SuperAdminGuard.test.tsx`

### Integration Tests
- `tests/api/test_jwt_integration.py`

### E2E Tests
- `cypress/e2e/super-admin-guard.cy.ts`

---

**Next Steps**: 
1. Monitor super admin access patterns
2. Implement audit logging for super admin actions
3. Consider adding time-limited super admin sessions for enhanced security
